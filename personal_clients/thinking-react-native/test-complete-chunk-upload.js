/**
 * 完整的分片上传流程测试
 * 模拟 React Native 环境中的完整分片上传过程
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// 模拟 React Native 的 RNFS
const mockRNFS = {
  TemporaryDirectoryPath: './temp',
  
  async copyFile(source, dest) {
    console.log(`📁 复制文件: ${source} -> ${dest}`);
    // 确保目录存在
    const dir = path.dirname(dest);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    if (source.startsWith('content://')) {
      // 模拟复制一个真实文件
      const testFile = './test-video.bin';
      if (!fs.existsSync(testFile)) {
        // 创建一个 5MB 的测试文件
        const buffer = Buffer.alloc(5 * 1024 * 1024);
        for (let i = 0; i < buffer.length; i++) {
          buffer[i] = i % 256;
        }
        fs.writeFileSync(testFile, buffer);
        console.log(`✅ 创建测试文件: ${testFile} (${buffer.length} bytes)`);
      }
      fs.copyFileSync(testFile, dest);
    } else {
      fs.copyFileSync(source, dest);
    }
  },
  
  async read(filePath, length, position, encoding) {
    console.log(`📖 读取文件: ${filePath}, position=${position}, length=${length}, encoding=${encoding}`);
    
    const fd = fs.openSync(filePath, 'r');
    const buffer = Buffer.alloc(length);
    const bytesRead = fs.readSync(fd, buffer, 0, length, position);
    fs.closeSync(fd);
    
    const actualBuffer = buffer.slice(0, bytesRead);
    console.log(`✅ 实际读取字节数: ${bytesRead}`);
    
    if (encoding === 'base64') {
      return actualBuffer.toString('base64');
    } else {
      return actualBuffer.toString(encoding || 'utf8');
    }
  },
  
  async writeFile(filePath, data, encoding) {
    console.log(`💾 写入文件: ${filePath}, encoding=${encoding}, 数据长度=${data.length}`);
    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, data, encoding);
    const stats = fs.statSync(filePath);
    console.log(`✅ 文件写入成功: ${stats.size} bytes`);
  },
  
  async unlink(filePath) {
    console.log(`🗑️ 删除文件: ${filePath}`);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }
};

// 模拟分片创建函数
async function createChunkFromFile(fileUri, start, size) {
  try {
    console.log(`\n🔧 创建分片: start=${start}, size=${size}, fileUri=${fileUri}`);
    
    // 对于 content:// URI，我们需要先复制到临时文件，然后读取分片
    let readableFilePath = fileUri;
    let needsCleanup = false;
    
    if (fileUri.startsWith('content://')) {
      // 创建临时文件路径
      const tempDir = mockRNFS.TemporaryDirectoryPath;
      const tempFileName = `temp_chunk_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const tempFilePath = `${tempDir}/${tempFileName}`;
      
      console.log(`📋 复制 content:// 文件到临时位置: ${tempFilePath}`);
      
      // 复制文件到临时位置
      await mockRNFS.copyFile(fileUri, tempFilePath);
      readableFilePath = tempFilePath;
      needsCleanup = true;
    }
    
    // 使用 RNFS.read 读取指定范围的数据
    console.log(`📖 读取文件分片: ${readableFilePath}, start=${start}, size=${size}`);
    const chunkData = await mockRNFS.read(readableFilePath, size, start, 'base64');
    
    // 验证读取的数据大小
    const decodedSize = Math.ceil(chunkData.length * 3 / 4); // base64 解码后的大小估算
    console.log(`✅ 分片创建成功: start=${start}, 请求大小=${size}, base64长度=${chunkData.length}, 估算解码大小=${decodedSize}`);
    
    // 清理临时文件
    if (needsCleanup) {
      try {
        await mockRNFS.unlink(readableFilePath);
        console.log(`🧹 清理临时文件: ${readableFilePath}`);
      } catch (cleanupError) {
        console.warn('⚠️ 清理临时文件失败:', cleanupError);
      }
    }
    
    // 直接返回 base64 字符串
    return chunkData;

  } catch (error) {
    console.error('❌ 创建文件分片失败:', error);
    throw new Error(`创建文件分片失败: ${error}`);
  }
}

// 模拟上传分片函数
async function uploadChunk(chunkIndex, chunkData) {
  try {
    console.log(`\n🚀 上传分片 ${chunkIndex}，数据类型: ${typeof chunkData}`);
    
    // 处理分片数据 - 将分片数据写入临时文件然后上传
    if (chunkData) {
      console.log(`📝 处理分片数据: 类型=${typeof chunkData}`);
      
      // 创建临时文件
      const tempDir = mockRNFS.TemporaryDirectoryPath;
      const tempFileName = `upload_chunk_${chunkIndex}_${Date.now()}.bin`;
      const tempFilePath = `${tempDir}/${tempFileName}`;
      
      // 将分片数据写入临时文件
      if (typeof chunkData === 'string') {
        // 如果是 base64 字符串，直接写入
        await mockRNFS.writeFile(tempFilePath, chunkData, 'base64');
        console.log(`💾 分片写入临时文件: ${tempFilePath}, base64长度=${chunkData.length}`);
      } else {
        // 如果是其他类型，尝试转换为字符串
        const dataStr = String(chunkData);
        await mockRNFS.writeFile(tempFilePath, dataStr, 'utf8');
        console.log(`💾 分片写入临时文件: ${tempFilePath}, 数据长度=${dataStr.length}`);
      }
      
      // 验证文件大小
      const stats = fs.statSync(tempFilePath);
      console.log(`✅ 临时文件大小: ${stats.size} bytes`);
      
      // 模拟上传（这里只是验证文件可以读取）
      const uploadData = fs.readFileSync(tempFilePath);
      console.log(`🎯 模拟上传成功: ${uploadData.length} bytes`);
      
      // 清理临时文件
      await mockRNFS.unlink(tempFilePath);
      
      return { success: true, size: uploadData.length };
    }
    
    return { success: false, error: '无效的分片数据' };
    
  } catch (error) {
    console.error('❌ 上传分片失败:', error);
    return { success: false, error: error.message };
  }
}

// 测试完整流程
async function testCompleteChunkUpload() {
  console.log('🚀 开始完整分片上传测试\n');
  
  try {
    // 模拟文件信息
    const fileUri = 'content://com.android.providers.media.documents/document/video%3A1000075413';
    const fileSize = 5 * 1024 * 1024; // 5MB
    const chunkSize = 1024 * 1024; // 1MB
    const totalChunks = Math.ceil(fileSize / chunkSize);
    
    console.log(`📊 文件信息:`);
    console.log(`   文件URI: ${fileUri}`);
    console.log(`   文件大小: ${fileSize} bytes (${(fileSize / 1024 / 1024).toFixed(2)}MB)`);
    console.log(`   分片大小: ${chunkSize} bytes (${(chunkSize / 1024).toFixed(0)}KB)`);
    console.log(`   总分片数: ${totalChunks}`);
    
    // 测试每个分片
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const size = Math.min(chunkSize, fileSize - start);
      
      console.log(`\n📦 测试分片 ${i + 1}/${totalChunks}:`);
      console.log(`   范围: ${start}-${start + size - 1}`);
      console.log(`   大小: ${size} bytes`);
      
      // 创建分片
      const chunkData = await createChunkFromFile(fileUri, start, size);
      
      // 上传分片
      const uploadResult = await uploadChunk(i + 1, chunkData);
      
      if (uploadResult.success) {
        console.log(`✅ 分片 ${i + 1} 上传成功: ${uploadResult.size} bytes`);
      } else {
        console.error(`❌ 分片 ${i + 1} 上传失败: ${uploadResult.error}`);
        break;
      }
    }
    
    console.log('\n🎉 完整分片上传测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testCompleteChunkUpload();
