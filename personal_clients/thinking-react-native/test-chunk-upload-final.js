#!/usr/bin/env node

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';

// 动态计算分片大小（与前端逻辑一致）
function calculateOptimalChunkSize(fileSize) {
  const MIN_CHUNK_SIZE = 64 * 1024;    // 64KB 最小分片
  const MAX_CHUNK_SIZE = 1 * 1024 * 1024; // 1MB 最大分片，确保nginx兼容
  
  // 根据用户需求，确保大文件至少有5%的进度增量，小文件也有合理的进度显示
  const MIN_PROGRESS_INCREMENTS = 20; // 至少20个进度点，即每个5%
  
  let idealChunkSize;
  
  // 对于小文件（小于1MB），确保至少有5个分片以显示进度
  if (fileSize <= MAX_CHUNK_SIZE) {
    const minChunksForSmallFiles = 5;
    idealChunkSize = Math.ceil(fileSize / minChunksForSmallFiles);
    // 但不能小于最小分片大小
    idealChunkSize = Math.max(MIN_CHUNK_SIZE, idealChunkSize);
  } else {
    // 对于大文件，确保至少有20个进度点（5%增量）
    idealChunkSize = Math.ceil(fileSize / MIN_PROGRESS_INCREMENTS);
  }
  
  // 限制在合理范围内
  idealChunkSize = Math.max(MIN_CHUNK_SIZE, idealChunkSize);
  idealChunkSize = Math.min(MAX_CHUNK_SIZE, idealChunkSize);
  
  const actualChunks = Math.ceil(fileSize / idealChunkSize);
  const progressIncrement = (100 / actualChunks).toFixed(1);
  
  console.log(`分片计算: 文件大小=${fileSize} bytes (${(fileSize/1024/1024).toFixed(2)}MB), 分片大小=${idealChunkSize} bytes (${(idealChunkSize/1024).toFixed(1)}KB), 分片数=${actualChunks}, 每个分片进度=${progressIncrement}%`);
  
  return idealChunkSize;
}

// 创建测试文件
function createTestFile(sizeInMB = 5) {
  const filename = path.join(__dirname, `test-chunk-upload-${Date.now()}.bin`);
  const sizeInBytes = sizeInMB * 1024 * 1024;
  
  console.log(`📄 创建测试文件: ${path.basename(filename)} (${sizeInMB}MB)`);
  
  // 创建指定大小的测试文件
  const buffer = Buffer.alloc(sizeInBytes, 'A');
  fs.writeFileSync(filename, buffer);
  
  return {
    filename: filename,
    name: path.basename(filename),
    size: sizeInBytes,
  };
}

// 测试完整的分片上传流程
async function testChunkUploadFlow() {
  let testFile = null;
  
  try {
    // 1. 创建测试文件
    testFile = createTestFile(3); // 3MB文件
    const chunkSize = calculateOptimalChunkSize(testFile.size);
    
    // 2. 初始化分片上传
    console.log('\n🚀 初始化分片上传...');
    const initResponse = await axios.post(`${API_BASE_URL}/drive/init-chunk-upload`, {
      file_name: testFile.name,
      file_size: testFile.size,
      parent_id: null,
      chunk_size: chunkSize
    }, {
      headers: {
        'Content-Type': 'application/json',
        'bullet': BULLET_HEADER,
      },
      timeout: 10000,
    });

    const taskId = initResponse.data.data.ID;
    const totalChunks = initResponse.data.data.TotalChunks;
    console.log(`✅ 初始化成功 - 任务ID: ${taskId}, 总分片数: ${totalChunks}`);
    
    // 3. 逐个上传分片
    console.log('\n📤 开始上传分片...');
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, testFile.size);
      
      const formData = new FormData();
      formData.append('task_id', taskId.toString()); // gateway期望小写下划线格式
      formData.append('chunk_index', i.toString()); // gateway期望小写下划线格式

      const chunkData = fs.createReadStream(testFile.filename, { start, end: end - 1 });
      formData.append('chunk_data', chunkData, `chunk_${i}`); // gateway期望小写下划线格式

      const chunkResponse = await axios.post(`${API_BASE_URL}/drive/upload-chunk`, formData, {
        headers: {
          ...formData.getHeaders(),
          'bullet': BULLET_HEADER,
        },
        timeout: 30000,
      });

      const progress = Math.round(((i + 1) / totalChunks) * 100);
      console.log(`✅ 分片 ${i + 1}/${totalChunks} 上传成功，进度: ${progress}%`);
      
      // 等待一下，让进度更新有时间传播
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    // 4. 完成分片上传
    console.log('\n🏁 完成分片上传...');
    const completeResponse = await axios.post(`${API_BASE_URL}/drive/complete-chunk-upload`, {
      task_id: taskId.toString(), // gateway期望小写下划线格式和字符串类型
    }, {
      headers: {
        'Content-Type': 'application/json',
        'bullet': BULLET_HEADER,
      },
      timeout: 30000,
    });

    console.log('🎉 分片上传测试成功完成!');
    console.log('上传的文件信息:', completeResponse.data.data);
    
    return {
      success: true,
      file: completeResponse.data.data,
      taskId: taskId,
    };
    
  } catch (error) {
    console.error('\n💥 分片上传测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    return {
      success: false,
      error: error.message,
    };
  } finally {
    // 清理测试文件
    if (testFile && fs.existsSync(testFile.filename)) {
      fs.unlinkSync(testFile.filename);
      console.log(`🗑️ 清理测试文件: ${testFile.name}`);
    }
  }
}

// 测试不同大小的文件
async function testMultipleSizes() {
  console.log('🧪 开始测试不同大小文件的分片上传...\n');
  
  const testCases = [
    { name: '小文件', sizeInMB: 0.5 },  // 512KB
    { name: '中等文件', sizeInMB: 2 },   // 2MB
    { name: '大文件', sizeInMB: 5 },     // 5MB
  ];
  
  let successCount = 0;
  let failCount = 0;
  
  for (const testCase of testCases) {
    console.log(`\n📋 测试 ${testCase.name} (${testCase.sizeInMB}MB):`);
    console.log('='.repeat(50));
    
    let testFile = null;
    try {
      testFile = createTestFile(testCase.sizeInMB);
      const chunkSize = calculateOptimalChunkSize(testFile.size);
      
      // 验证分片计算是否符合要求
      const actualChunks = Math.ceil(testFile.size / chunkSize);
      const progressIncrement = 100 / actualChunks;
      
      if (progressIncrement >= 5.0 || testFile.size <= 1024 * 1024) {
        console.log(`✅ 分片计算符合要求: 每个分片进度 ${progressIncrement.toFixed(1)}%`);
      } else {
        console.log(`⚠️ 分片计算可能需要优化: 每个分片进度 ${progressIncrement.toFixed(1)}%`);
      }
      
      successCount++;
    } catch (error) {
      console.error(`❌ ${testCase.name} 测试失败:`, error.message);
      failCount++;
    } finally {
      if (testFile && fs.existsSync(testFile.filename)) {
        fs.unlinkSync(testFile.filename);
      }
    }
  }
  
  console.log('\n📊 测试总结:');
  console.log(`✅ 成功: ${successCount}`);
  console.log(`❌ 失败: ${failCount}`);
  console.log(`📈 成功率: ${((successCount / (successCount + failCount)) * 100).toFixed(1)}%`);
}

// 主函数
async function main() {
  console.log('🚀 开始分片上传功能测试\n');
  
  try {
    // 测试完整的分片上传流程
    const result = await testChunkUploadFlow();
    
    if (result.success) {
      console.log('\n✅ 完整分片上传流程测试通过');
      
      // 测试不同大小文件的分片计算
      await testMultipleSizes();
    } else {
      console.log('\n❌ 完整分片上传流程测试失败');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  }
  
  console.log('\n🎉 所有测试完成!');
}

// 运行测试
if (require.main === module) {
  main();
}
