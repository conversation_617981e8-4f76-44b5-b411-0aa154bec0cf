/**
 * React Native 分片上传完整流程测试
 * 模拟 React Native 环境中的文件分片处理
 */

const fs = require('fs');
const path = require('path');

// 模拟 React Native 的 RNFS
const mockRNFS = {
  TemporaryDirectoryPath: '/tmp',
  
  async copyFile(source, dest) {
    console.log(`模拟复制文件: ${source} -> ${dest}`);
    // 在真实环境中，这里会复制 content:// 文件到临时位置
    // 我们这里直接复制测试文件
    if (source.startsWith('content://')) {
      // 模拟复制一个真实文件
      const testFile = './test-file.bin';
      if (!fs.existsSync(testFile)) {
        // 创建一个测试文件
        const buffer = Buffer.alloc(5 * 1024 * 1024); // 5MB
        for (let i = 0; i < buffer.length; i++) {
          buffer[i] = i % 256;
        }
        fs.writeFileSync(testFile, buffer);
        console.log(`创建测试文件: ${testFile} (${buffer.length} bytes)`);
      }
      fs.copyFileSync(testFile, dest);
    } else {
      fs.copyFileSync(source, dest);
    }
  },
  
  async read(filePath, length, position, encoding) {
    console.log(`读取文件: ${filePath}, position=${position}, length=${length}, encoding=${encoding}`);
    
    const fd = fs.openSync(filePath, 'r');
    const buffer = Buffer.alloc(length);
    const bytesRead = fs.readSync(fd, buffer, 0, length, position);
    fs.closeSync(fd);
    
    const actualBuffer = buffer.slice(0, bytesRead);
    console.log(`实际读取字节数: ${bytesRead}`);
    
    if (encoding === 'base64') {
      return actualBuffer.toString('base64');
    } else {
      return actualBuffer.toString(encoding || 'utf8');
    }
  },
  
  async unlink(filePath) {
    console.log(`删除文件: ${filePath}`);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }
};

// 模拟 React Native 的 Blob
class MockBlob {
  constructor(parts) {
    this.parts = parts;
    this.size = 0;
    
    for (const part of parts) {
      if (part instanceof Uint8Array) {
        this.size += part.length;
      } else if (typeof part === 'string') {
        this.size += Buffer.byteLength(part, 'utf8');
      }
    }
  }
}

// Base64 解码方法（与 DriveService 中的相同）
function base64Decode(base64) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  let result = '';
  let i = 0;
  
  // 移除非 base64 字符
  base64 = base64.replace(/[^A-Za-z0-9+/]/g, '');
  
  while (i < base64.length) {
    const encoded1 = chars.indexOf(base64.charAt(i++));
    const encoded2 = chars.indexOf(base64.charAt(i++));
    const encoded3 = chars.indexOf(base64.charAt(i++));
    const encoded4 = chars.indexOf(base64.charAt(i++));
    
    const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;
    
    result += String.fromCharCode((bitmap >> 16) & 255);
    if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
    if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255);
  }
  
  return result;
}

// 模拟 createChunkFromFile 方法
async function createChunkFromFile(fileUri, start, size) {
  try {
    console.log(`\n=== 创建分片 ===`);
    console.log(`fileUri: ${fileUri}`);
    console.log(`start: ${start}`);
    console.log(`size: ${size}`);
    
    // 对于 content:// URI，我们需要先复制到临时文件，然后读取分片
    let readableFilePath = fileUri;
    let needsCleanup = false;
    
    if (fileUri.startsWith('content://')) {
      // 创建临时文件路径
      const tempDir = mockRNFS.TemporaryDirectoryPath;
      const tempFileName = `temp_chunk_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const tempFilePath = `${tempDir}/${tempFileName}`;
      
      console.log(`复制 content:// 文件到临时位置: ${tempFilePath}`);
      
      // 复制文件到临时位置
      await mockRNFS.copyFile(fileUri, tempFilePath);
      readableFilePath = tempFilePath;
      needsCleanup = true;
    }
    
    // 使用 RNFS.read 读取指定范围的数据
    console.log(`读取文件分片: ${readableFilePath}, start=${start}, size=${size}`);
    const chunkData = await mockRNFS.read(readableFilePath, size, start, 'base64');
    
    console.log(`Base64 数据长度: ${chunkData.length}`);
    console.log(`Base64 数据前50字符: ${chunkData.substring(0, 50)}...`);
    
    // 将 base64 数据转换为 Blob
    // 在 React Native 中，我们需要手动实现 base64 解码
    const binaryString = base64Decode(chunkData);
    console.log(`解码后二进制字符串长度: ${binaryString.length}`);
    
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const chunkBlob = new MockBlob([bytes]);
    
    console.log(`分片创建成功: start=${start}, 请求大小=${size}, 实际大小=${chunkBlob.size}`);
    
    // 验证分片大小是否正确
    if (chunkBlob.size !== size) {
      console.error(`❌ 分片大小不匹配! 期望: ${size}, 实际: ${chunkBlob.size}`);
    } else {
      console.log(`✅ 分片大小正确: ${chunkBlob.size}`);
    }
    
    // 清理临时文件
    if (needsCleanup) {
      try {
        await mockRNFS.unlink(readableFilePath);
        console.log(`清理临时文件: ${readableFilePath}`);
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
    
    return chunkBlob;

  } catch (error) {
    console.error('创建文件分片失败:', error);
    throw new Error(`创建文件分片失败: ${error}`);
  }
}

// 测试函数
async function testChunkCreation() {
  console.log('🚀 开始 React Native 分片创建测试\n');
  
  try {
    // 模拟 content:// URI
    const fileUri = 'content://com.android.providers.media.documents/document/video%3A1000075413';
    const chunkSize = 1048576; // 1MB
    
    // 测试创建第一个分片
    console.log('📋 测试第一个分片 (0-1MB):');
    const chunk1 = await createChunkFromFile(fileUri, 0, chunkSize);
    
    // 测试创建第二个分片
    console.log('\n📋 测试第二个分片 (1MB-2MB):');
    const chunk2 = await createChunkFromFile(fileUri, chunkSize, chunkSize);
    
    // 测试创建最后一个分片（可能小于 chunkSize）
    console.log('\n📋 测试最后一个分片:');
    const fileSize = 5 * 1024 * 1024; // 5MB 测试文件
    const lastChunkStart = Math.floor(fileSize / chunkSize) * chunkSize;
    const lastChunkSize = fileSize - lastChunkStart;
    const lastChunk = await createChunkFromFile(fileUri, lastChunkStart, lastChunkSize);
    
    console.log('\n🎉 所有测试完成!');
    console.log(`第一个分片大小: ${chunk1.size}`);
    console.log(`第二个分片大小: ${chunk2.size}`);
    console.log(`最后分片大小: ${lastChunk.size}`);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testChunkCreation();
